:root {
  --accent-base-color: #85be40;
  --accent-fill-hover: #488100;

  --primary-color-1: #00ab55;

  --primary-color-hover-1: #007b55;
  --primary-color-hover-2: #ebf9f2;

  --color-text-1: #212529;
  --color-text-2: #fff;
  --color-text-3: #71717a;
  --color-text-4: #515151;

  --color-icon-1: #919eab;
  --color-icon-2: #414141;

  --color-hover-1: #e2e8f0;

  --border-color-1: #919eab3d;

  --background-color-1: #ffffff;
  --background-color-2: #edeff1;
  --background-color-3: #f2f3f5;
  --background-color-4: #fbfbfc;

  --scroll-color-track-1: transparent;
  --scroll-color-thumb-1: #cbd0d6;
  --scroll-color-thumb-hover-1: #b6bbc0;

  --dropdown-background-color-1: #ebebeb;
  --dropdown-background-color-2: #d7d7d7;
}
.darkmode {
  --color-text-1: #fff;
  --color-text-3: #e4e4e7;
  --color-text-4: #c9c9c9;

  --color-hover-1: #475569;

  --color-icon-2: #fff;

  --primary-color-hover-2: #1c3f3b;

  --color-icon-1: #e2e8f0;

  --background-color-1: #161c24;
  --background-color-2: #333d48;
  --background-color-3: #29333c;
  --background-color-4: #212b36;

  --scroll-color-thumb-1: #46525f;
  --scroll-color-thumb-hover-1: #37424d;

  --dropdown-background-color-1: #3b4957;
  --dropdown-background-color-2: #283543;
}
html,
body {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.5;
  margin: 0;
  padding: 0;
  font-family: inherit;
}
.dpu-container-view .darkmode {
  overflow: hidden;
  color: var(--color-text-1);
}

.dpu-container-view {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-color: var(--background-color-1);
}

.dpu-container-view > * {
  box-sizing: border-box;
}

.dpu-container-view *::-webkit-scrollbar {
  width: 5px;
  height: 5px; /* Scroll ngang */
}

.dpu-container-view *::-webkit-scrollbar-track {
  background: var(--scroll-color-track-1);
}

.dpu-container-view *::-webkit-scrollbar-thumb {
  background: var(--scroll-color-thumb-1);
}

.dpu-container-view *::-webkit-scrollbar-thumb:hover {
  background: var(--scroll-color-thumb-hover-1);
}

.dpu-viewer {
  width: 100%;
  height: 100%;
}

.custom-toolbar-dpu {
  position: absolute;
  right: 5px;
  top: 100px;
  z-index: 5;
  color: white;
  display: flex;
  flex-direction: column;
}

.container-tool-cesium {
  display: flex;
  flex-direction: column;
  padding: 2px;
  border-radius: 4px;
  background-color: var(--background-color-1);
  box-shadow: 1px 3px 10px 0 rgba(0, 0, 0, 0.5);
  opacity: 0.85;
  border: none;
  margin-top: 5px;
}

/* Tooltip Styles */
.dynamic-tooltip {
  position: absolute;
  background-color: var(--background-color-1);
  color: var(--color-icon-2);
  -webkit-box-shadow: 0px 0px 15px 0px rgba(170, 170, 170, 1);
  -moz-box-shadow: 0px 0px 15px 0px rgba(170, 170, 170, 1);
  box-shadow: 0px 0px 15px 0px rgba(170, 170, 170, 1);
  padding: 5px;
  border-radius: 4px;
  white-space: nowrap;
  font-size: 12px;
  z-index: 999;
  opacity: 0.85;
  transition: opacity 0.3s ease;
  text-align: right;
  direction: rtl;
}

/* Các hướng mũi tên */
.dynamic-tooltip-arrow-right,
.dynamic-tooltip-arrow-left,
.dynamic-tooltip-arrow-top,
.dynamic-tooltip-arrow-bottom,
.dynamic-tooltip-arrow-topLeft,
.dynamic-tooltip-arrow-topRight,
.dynamic-tooltip-arrow-bottomLeft,
.dynamic-tooltip-arrow-bottomRight {
  position: absolute;
}

/* Mũi tên bên phải */
.dynamic-tooltip-arrow-right::after {
  content: "";
  position: absolute;
  top: 50%;
  right: -5px;
  transform: translateY(-50%);
  border-top: 7px solid transparent;
  border-left: 10px solid var(--background-color-1);
  border-bottom: 7px solid transparent;
}

/* Mũi tên bên trái */
.dynamic-tooltip-arrow-left::after {
  content: "";
  position: absolute;
  top: 50%;
  left: -5px;
  transform: translateY(-50%);
  border-top: 7px solid transparent;
  border-right: 10px solid var(--background-color-1);
  border-bottom: 7px solid transparent;
}

/* Mũi tên phía trên */
.dynamic-tooltip-arrow-top::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 10px solid var(--background-color-1);
}

/* Mũi tên phía dưới */
.dynamic-tooltip-arrow-bottom::after {
  content: "";
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 10px solid var(--background-color-1);
}

/* Mũi tên phía trên bên trái */
.dynamic-tooltip-arrow-topLeft::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0px;
  transform: translateX(0);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 10px solid var(--background-color-1);
}

/* Mũi tên phía trên bên phải */
.dynamic-tooltip-arrow-topRight::after {
  content: "";
  position: absolute;
  bottom: -5px;
  right: 0px;
  transform: translateX(0);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 10px solid var(--background-color-1);
}

/* Mũi tên phía dưới bên trái */
.dynamic-tooltip-arrow-bottomLeft::after {
  content: "";
  position: absolute;
  top: -5px;
  left: 0px;
  transform: translateX(0);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 10px solid var(--background-color-1);
}

/* Mũi tên phía dưới bên phải */
.dynamic-tooltip-arrow-bottomRight::after {
  content: "";
  position: absolute;
  top: -5px;
  right: 0px;
  transform: translateX(0);
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 10px solid var(--background-color-1);
}

.btn-tool-cesium {
  border: 1px solid rgba(34, 34, 34, 0);
  border-radius: 4px;
  cursor: pointer;
  float: left;
  height: 28px;
  text-align: center;
  position: relative;
  width: 28px;
  padding: 2px 2px;
  background-color: transparent;
  line-height: 1px;
}

.btn-tool-cesium svg {
  width: 100%;
  height: 100%;
  stroke: var(--color-icon-2);
  fill: var(--color-icon-2);
}

.btn-tool-cesium:hover svg {
  stroke: var(--primary-color-1);
  fill: var(--primary-color-1);
}

.btn-tool-cesium.done {
  background-color: var(--primary-color-1);
  font-size: unset;
  width: 28px;
  height: 28px;
}

.btn-tool-cesium.done:hover {
  background-color: var(--primary-color-hover-1);
  border: 1px solid var(--primary-color-hover-1);
  color: #fff;
}
.btn-tool-cesium.done:hover svg {
  stroke: #fff;
  fill: #fff;
}

.btn-tool-cesium:hover {
  background-color: transparent;
  border: 1px solid var(--primary-color-1);
  color: var(--primary-color-1);
}

.btn-tool-cesium:focus {
  background-color: transparent;
  border-radius: 3px;
  box-shadow: inset 0 2px 2px 0 transparent;
  outline: none;
  border-color: transparent;
}

.btn-tool-cesium:focus:hover {
  border: 1px solid var(--primary-color-1);
}

.btn-tool-cesium:focus:hover svg {
  stroke: var(--primary-color-1);
  fill: var(--primary-color-1);
}

.btn-tool-cesium.active {
  background-color: transparent;
  border-radius: 3px;

  color: var(--primary-color-1);
  border: 1px solid var(--primary-color-1);
  outline: none;
}

.btn-tool-cesium.active svg {
  stroke: var(--primary-color-1);
  fill: var(--primary-color-1);
}

.zoom-earth {
  position: absolute;
  right: 5px;
  z-index: 5;
  color: white;
}

.zoom-earth.in {
  bottom: 105px;
}

.zoom-earth.out {
  bottom: 65px;
}
.zoom-earth.in.resp-zoom-earth-in-h550 {
  top: 50px;
  left: 5px;
  right: unset;
  bottom: unset;
}

.zoom-earth.out.resp-zoom-earth-out-h550 {
  top: 87px;
  left: 5px;
  right: unset;
  bottom: unset;
}

.body-dialog-valid-token {
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;
}
.icon-dialog-error {
  background-color: #fff;
  border-radius: 50%;
  flex-shrink: 0;
}
.icon-dialog-error svg {
  width: 100%;
  height: 100%;
  fill: #d75a4a;
  stroke: #d75a4a;
}
input.dpu-input-check-box + label {
  display: block;
  cursor: pointer;
}

/* Ẩn checkbox thực tế */
input.dpu-input-check-box[type="checkbox"] {
  display: none;
}

/* Tạo kiểu cho phần tử label:before */
input.dpu-input-check-box[type="checkbox"] + label:before {
  content: "\2713"; /* Biểu tượng checkmark */
  border: 1px solid #ced4da;
  border-radius: 0.2em;
  display: inline-block;
  font-size: 14px;
  width: 16px;
  height: 16px;
  margin-right: 10px;
  color: transparent; /* Màu chữ khi chưa được chọn */
  text-align: center;
  line-height: 14px;
}

/* Khi checkbox được chọn, thay đổi màu nền và màu chữ của label:before */
input.dpu-input-check-box[type="checkbox"]:checked + label:before {
  background-color: var(--primary-color-1);
  color: #fff;
}
input.dpu-input-text[type="number"]:focus,
input.dpu-input-text[type="number"]:focus-visible,
input.dpu-input-text[type="text"]:focus,
input.dpu-input-text[type="text"]:focus-visible {
  border: 1px solid var(--primary-color-1);
}

input.dpu-input-text[type="number"],
input.dpu-input-text[type="text"] {
  color: var(--color-text-1);
  width: 100%;
  height: 30px;
  border: 1px solid #ced4da;
  background: none;
  box-sizing: border-box;
  border-radius: 4px;
  padding-block: 0px;
  padding-inline: 0;

  padding-left: 8px;
  outline: none;
  box-shadow: none;
}
input.dpu-input-text[type="number"]::placeholder,
input.dpu-input-text[type="text"]::placeholder {
  color: var(--color-text-4);
}
input.dpu-inpu-color[type="color"] {
  background: var(--background-color-3);
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 0px 2px;
}

input.dpu-input-text[type="number"]::-webkit-outer-spin-button,
input.dpu-input-text[type="number"]::-webkit-inner-spin-button {
  width: 25px;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  color: red;
}

/*********** Baseline, reset styles ***********/
input.dpu-input-range[type="range"] {
  width: 100%;
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

/* Removes default focus */
input.dpu-input-range[type="range"]:focus {
  outline: none;
}

/******** Chrome, Safari, Opera and Edge Chromium styles ********/
/* slider track */
input.dpu-input-range[type="range"]::-webkit-slider-runnable-track {
  background-color: var(--background-color-3);
  border-radius: 5px;
  height: 5px;
}

/* slider thumb */
input.dpu-input-range[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none; /* Override default look */
  appearance: none;
  margin-top: -5.5px; /* Centers thumb on the track */
  background-color: var(--primary-color-1);
  border-radius: 50%;
  height: 12px;
  width: 12px;
}

input.dpu-input-range[type="range"]:focus::-webkit-slider-thumb {
  outline: 2px solid var(--primary-color-1);
  outline-offset: 2px;
}

/*********** Firefox styles ***********/
/* slider track */
input.dpu-input-range[type="range"]::-moz-range-track {
  background-color: var(--background-color-3);
  border-radius: 5px;
  height: 5px;
}

/* slider thumb */
input.dpu-input-range[type="range"]::-moz-range-thumb {
  background-color: var(--primary-color-1);
  border: none; /*Removes extra border that FF applies*/
  border-radius: 50%;
  height: 12px;
  width: 12px;
}

input.dpu-input-range[type="range"]:focus::-moz-range-thumb {
  outline: 2px solid var(--primary-color-1);
  outline-offset: 2px;
}

.dpu-select-box {
  position: relative;
  display: block;
  width: 100%;
  margin: 0 auto;
  font-size: 12px;
  color: #60666d;
}

.dpu-select-box__current {
  position: relative;
  cursor: pointer;
  outline: none;
  border-radius: 5px;
}

.dpu-select-box__current:focus + .dpu-select-box__list {
  opacity: 1;
  animation-name: none;
}

.dpu-select-box__current:focus .dpu-select-box__option {
  cursor: pointer;
}

.dpu-select-box__icon {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  width: 10px;
  opacity: 0.3;
  transition: 0.2s ease;
}

.dpu-select-box__value {
  display: flex;
}

.dpu-select-box__input {
  display: none;
}

.dpu-select-box__input:checked + .dpu-select-box__input-text {
  display: block;
}

.dpu-select-box__input-text {
  display: none;
  width: 100%;
  margin: 0;
  padding: 0 5px;
  height: 30px;
  background-color: #fff;
  line-height: 30px;
  border-radius: 5px;
  border: 1px solid #ccc;
}

.dpu-select-box__list {
  position: absolute;
  width: 100%;
  padding: 0;
  list-style: none;
  opacity: 0;
  animation-name: dpuHideList;
  animation-duration: 0.5s;
  animation-delay: 0.5s;
  animation-fill-mode: forwards;
  animation-timing-function: step-start;
  background-color: #fff;
  max-height: 300px;
  overflow: auto;
  border-radius: 5px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.dpu-select-box__option {
  display: block;
  padding: 10px;
  /* background-color: #fff; */
}

.dpu-container-title {
  display: flex;
  align-items: center;
}

.dpu-container-title div:first-child {
  width: 3px;
  height: 15px;

  margin-right: 5px;
}

.dpu-select-box__option:hover,
.dpu-select-box__option:focus {
  color: #546c84;
  background-color: #fbfbfb;
}

@keyframes dpuHideList {
  from {
    transform: scaleY(1);
  }
  to {
    transform: scaleY(0);
  }
}
.dpu-tree-item.active .dpu-loading-animation {
  /* filter: grayscale(100%); */
  filter: grayscale(100%) brightness(1000%);
}

.dpu-loading-animation {
  animation: dpu-rotate-loading 1s linear infinite;
  max-width: none;
}

@keyframes dpu-rotate-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
