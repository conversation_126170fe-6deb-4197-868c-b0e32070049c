import { CityJSONLoaderPlugin, GLTFLoaderPlugin, Viewer } from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";

export class SceneManager {
    private _store = GlobalStore.getInstance().getAll();
    private _viewer: Viewer | undefined;
    private _gltfLoader: GLTFLoaderPlugin | undefined;
    private _cityJsonLoader: CityJSONLoaderPlugin | undefined; 
    constructor() {
        this._viewer = this._store.viewer;
        if (!this._viewer) {
            console.error("Viewer not initialized");
            return;
        }
        if (!this._store.gltfLoaderPlugin) {
            this._store.gltfLoaderPlugin = new GLTFLoaderPlugin(this._viewer);
            this._gltfLoader = this._store.gltfLoaderPlugin;
        }
        GlobalStore.getInstance().set("sceneManager", this);
    }

    public gtlfLoad(modelId: string, modelSrc: string) {    

        if (!this._gltfLoader) {
            this._gltfLoader = new GLTFLoaderPlugin(this._viewer!);
        }
        const sceneModel = this._gltfLoader.load({
            id: modelId,
            src: modelSrc,            
            edges: true
        });

        return sceneModel;
    }
    public cityJsonLoad(modelId: string, modelSrc: any) {
        if (!this._cityJsonLoader) {
            this._cityJsonLoader = new CityJSONLoaderPlugin(this._viewer!);
        }
        const sceneModel = this._cityJsonLoader.load({
            id: modelId,
            src: modelSrc,

        });

        return sceneModel;
    }

    public xktLoad(modelId: string, modelSrc: string) {

    }

}