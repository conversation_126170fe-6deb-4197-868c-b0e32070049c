import {
    Entity,
    MarqueePicker,
    MarqueePickerMouseControl,

    ObjectsKdTree3,
} from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";
import { overlappingPick } from "../../../libs/overlappingPick";
import { math } from "../../../libs/math";

export class EventHandler {
    private _store = GlobalStore.getInstance().getAll();
    private _canvas: HTMLCanvasElement | null = null;

    selectedEntities: Entity[] = [];
    private _lastColorizes: Map<string, number[]> = new Map();
    private marqueePicker: MarqueePicker | undefined;
    private marqueeControl: MarqueePickerMouseControl | undefined;

    private _isDragging = false;
    private _mouseDownPos: { x: number; y: number } | null = null;

    constructor() {
        this.initialize();
        this.setupMarqueePicker();
        GlobalStore.getInstance().set("eventHandler", this);
    }

    //#region setupMarqueePicker
    setupMarqueePicker() {
        const view = this._store.viewer!;
        const objectsKdTree3 = new ObjectsKdTree3({ viewer: view });

        this.marqueePicker = new MarqueePicker({
            viewer: view,
            objectsKdTree3,
        });

        this.marqueeControl = new MarqueePickerMouseControl({
            marqueePicker: this.marqueePicker,
        });
        this.marqueeControl.setActive(true);

        this.marqueePicker.on("clear", () => {
            this.clearSelections();
        });

        this.marqueePicker.on("picked", (objectIds: (string | number)[]) => {
            const ids = objectIds.map((id) => String(id));
            ids.forEach((id) => {
                const entity = view.scene.objects[id];
                if (entity) {
                    if (entity.xrayed) {
                        return; // Bỏ qua entity đang XRay
                    }

                    if (!this._lastColorizes.has(id)) {
                        this._lastColorizes.set(
                            id,
                            entity.colorize ? [...entity.colorize] : [1, 1, 1, 1]
                        );
                    }

                    if (!this.selectedEntities.find((ent) => ent.id === id)) {
                        this.selectedEntities.push(entity);
                    }
                }
            });

            // Chỉ set selected với những entity không xray
            const selectedIds = ids.filter((id) => {
                const entity = view.scene.objects[id];
                return entity && !entity.xrayed;
            });

            view.scene.setObjectsSelected(selectedIds, true);
        });

    }

    //#region initialize
    initialize() {
        this._canvas = this._store.canvas ?? null;
        if (!this._canvas) {
            console.error("Canvas not found in store.");
            return;
        }
        this._canvas.addEventListener("mousedown", this._handleMouseDown);
        this._canvas.addEventListener("mousemove", this._handleMouseMove);
        this._canvas.addEventListener("mouseup", this._handleMouseUp);
    }

    private _handleMouseDown = (event: MouseEvent) => {
        if (event.button !== 0) return;
        this._mouseDownPos = { x: event.offsetX, y: event.offsetY };
        this._isDragging = false;
    };

    private _handleMouseMove = (event: MouseEvent) => {
        if (!this._mouseDownPos) return;
        const dx = Math.abs(event.offsetX - this._mouseDownPos.x);
        const dy = Math.abs(event.offsetY - this._mouseDownPos.y);
        const distance = Math.sqrt(dx * dx + dy * dy);
        if (distance > 5) this._isDragging = true;
    };

    private _handleMouseUp = (event: MouseEvent) => {
        if (event.button !== 0 || !this._mouseDownPos) return;
        if (!this._isDragging) this._handleClick(event);
        this._mouseDownPos = null;
        this._isDragging = false;
    };

    //#region _handleClick   

    // private _handleClick(event: MouseEvent) {
    //     const viewer = this._store.viewer!;
    //     const coords = [event.offsetX, event.offsetY];
    //     const hit = viewer.scene.pick({ canvasPos: coords });

    //     if (hit && hit.entity && !hit.entity.xrayed) {
    //         const clickedEntity = hit.entity;
    //         const index = this.selectedEntities.findIndex(
    //             (ent) => ent.id === clickedEntity.id
    //         );

    //         if (event.ctrlKey) {
    //             // Giữ Ctrl → toggle select
    //             if (index !== -1) {
    //                 this._deselectEntity(clickedEntity);
    //             } else {
    //                 this._addEntity(clickedEntity);
    //             }
    //         } else {
    //             // Không Ctrl → bỏ hết và chọn mới
    //             this._clearSelections();
    //             this._addEntity(clickedEntity);
    //         }
    //     } else {
    //         // Click ngoài canvas, bỏ hết nếu không giữ Ctrl
    //         if (!event.ctrlKey) {
    //             this._clearSelections();
    //         }
    //     }
    // }
    private _handleClick(event: MouseEvent) {
        const viewer = this._store.viewer!;
        const scene = viewer.scene;
        const camera = viewer.camera;
        const canvas = this._canvas!;

        const origin = math.vec3();
        const direction = math.vec3();

        // Reset overlappingPick trước mỗi click
        overlappingPick.reset();

        // Tính ray từ chuột
        math.canvasPosToWorldRay(
            canvas,
            camera.viewMatrix,
            camera.projMatrix,
            camera.projection,
            [event.clientX, event.clientY],
            origin,
            direction
        );

        let selectedEntity: Entity | null = null;

        while (true) {
            const hit = overlappingPick.pick(scene, { origin, direction }, { wrapAround: false });
            if (!hit || !hit.entity) break;

            if (!hit.entity.xrayed) {
                selectedEntity = hit.entity;
                break; // gặp thằng không xray đầu tiên thì break
            }
        }

        if (selectedEntity) {
            const index = this.selectedEntities.findIndex(ent => ent.id === selectedEntity!.id);

            if (event.ctrlKey) {
                if (index !== -1) {
                    this._deselectEntity(selectedEntity);
                } else {
                    this._addEntity(selectedEntity);
                }
            } else {
                this.clearSelections();
                this._addEntity(selectedEntity);
            }
        } else {
            if (!event.ctrlKey) {
                this.clearSelections();
            }
        }
    }






    //#region _deselectEntity
    private _deselectEntity(entity: Entity) {
        const viewer = this._store.viewer!;
        const key = String(entity.id);
        const oldColor = this._lastColorizes.get(key);
        if (oldColor) {
            entity.colorize = oldColor;
        }
        viewer.scene.setObjectsSelected([key], false);
        this._lastColorizes.delete(key);
        this.selectedEntities = this.selectedEntities.filter(
            (ent) => ent.id !== entity.id
        );
    }

    //#region _addEntity
    private _addEntity(entity: Entity) {
        const viewer = this._store.viewer!;
        const key = String(entity.id);
        if (!this._lastColorizes.has(key)) {
            this._lastColorizes.set(
                key,
                entity.colorize ? [...entity.colorize] : [1, 1, 1, 1]
            );
        }
        viewer.scene.setObjectsSelected([key], true);
        if (!this.selectedEntities.find((ent) => ent.id === entity.id)) {
            this.selectedEntities.push(entity);
        }
    }

    //#region _clearSelections
    clearSelections() {
        const viewer = this._store.viewer!;
        this.selectedEntities.forEach((entity) => {
            const key = String(entity.id);
            const oldColor = this._lastColorizes.get(key);
            if (oldColor) {
                entity.colorize = oldColor;
            }
        });
        const deselectIds = this.selectedEntities.map((ent) => String(ent.id));
        viewer.scene.setObjectsSelected(deselectIds, false);
        this.selectedEntities = [];
        this._lastColorizes.clear();
    }

    //#region destroy
    public destroy() {
        if (this._canvas) {
            this._canvas.removeEventListener("mousedown", this._handleMouseDown);
            this._canvas.removeEventListener("mousemove", this._handleMouseMove);
            this._canvas.removeEventListener("mouseup", this._handleMouseUp);
            this._canvas = null;
        }
        this.clearSelections();
        this._isDragging = false;
        this._mouseDownPos = null;
        this.marqueeControl?.setActive(false);
    }
}
