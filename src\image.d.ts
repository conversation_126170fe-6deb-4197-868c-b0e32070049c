// images.d.ts
declare module "*.jpg" {
  const value: string;
  export default value;
}

declare module "*.png" {
  const value: string;
  export default value;
}

declare module "*.svg" {
  const value: string;
  export default value;
}

declare module "*.gif" {
  const value: string;
  export default value;
}
// Tạo khai báo module cho các tệp .gltf
declare module "*.glb" {
  const value: string;  // Hoặc bạn có thể sử dụng kiểu phù hợp với cấu trúc của tệp .gltf
  export default value;
}

declare module "worker-loader!*" {
    class WebpackWorker extends Worker {
      constructor();
    }
    export default WebpackWorker;
  }