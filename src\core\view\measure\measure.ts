import { DistanceMeasurementsMouseControl, DistanceMeasurementsPlugin } from "@xeokit/xeokit-sdk";
import GlobalStore from "../../../core/globalStore/globalStore";

export class Measure {
    private _store = GlobalStore.getInstance().getAll();
    distanceMeasurementsPlugin = new DistanceMeasurementsPlugin(this._store.viewer!,{
        container: this._store.containerCanvas as HTMLElement,
    });
    private distanceMeasurementsMouseControl: DistanceMeasurementsMouseControl | undefined; // Để sử dụng sau này
    constructor() {

        GlobalStore.getInstance().set("measure", this);
    }

    createDistanceMeasurement = () => {

        console.log("createDisstanceMeasurement", this.distanceMeasurementsPlugin);
        this.distanceMeasurementsPlugin.on("measurementStart", (distanceMeasurement) => {
            console.log("measurementStart");
            console.log("origin", distanceMeasurement.origin.entity);
            console.log("target", distanceMeasurement.target.entity);
        });

        this.distanceMeasurementsPlugin.on("measurementEnd", (distanceMeasurement) => {
            console.log("measurementEnd");
            console.log("origin", distanceMeasurement.origin.entity);
            console.log("target", distanceMeasurement.target.entity);
        });

        this.distanceMeasurementsPlugin.on("measurementCancel", (distanceMeasurement) => {
            console.log("measurementCancel");
            console.log("origin", distanceMeasurement.origin.entity);
            console.log("target", distanceMeasurement.target.entity);
        });

        if (!this.distanceMeasurementsMouseControl) {
            this.distanceMeasurementsMouseControl = new DistanceMeasurementsMouseControl(this.distanceMeasurementsPlugin, {
                snapping: true // Default
            })

        }
        this.distanceMeasurementsMouseControl.snapping = true; // Bật snapping

        this.distanceMeasurementsMouseControl?.activate();
    }
}