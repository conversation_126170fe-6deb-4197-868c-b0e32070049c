import { GLTFLoaderPlugin, Viewer } from "@xeokit/xeokit-sdk";
import { EventHandler } from "../view/interaction/eventHandler";
import { ContextMenu } from "../view/interaction/contextMenu";
import { Toolbar } from "../view/toolbar/toolbar";
import { Measure } from "../view/measure/measure";

export type StoreConfig = {
    idViewer: string;
    idGeneral?: string;
    token?: string;
    containerViewer?: Element;
    containerId?: string;
    containerCanvas?: Element;
    canvasId?: string;
    canvas?: HTMLCanvasElement;
    viewer?: Viewer;
    eventHandler?: EventHandler
    contextMenu?: ContextMenu;
    gltfLoaderPlugin?: GLTFLoaderPlugin
    toolbar?: Toolbar
    measure?: Measure
    [key: string]: any;
};

class GlobalStore {
    private static instance: GlobalStore;
    private store: StoreConfig;

    private constructor(config: StoreConfig) {
        this.store = { ...config };
    }

    public static init(config: StoreConfig): GlobalStore {
        if (!GlobalStore.instance) {
            GlobalStore.instance = new GlobalStore(config);
        }
        return GlobalStore.instance;
    }

    public static getInstance(): GlobalStore {
        if (!GlobalStore.instance) {
            throw new Error("GlobalStore chưa init.");
        }
        return GlobalStore.instance;
    }

    public set<K extends keyof StoreConfig>(key: K, value: StoreConfig[K]) {
        this.store[key] = value;
    }

    public get<K extends keyof StoreConfig>(key: K): StoreConfig[K] {
        return this.store[key];
    }

    public getAll() {
        return this.store;
    }
}

export default GlobalStore;
