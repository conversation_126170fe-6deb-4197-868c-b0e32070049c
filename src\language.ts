// language.ts
import enMessages from './assets/json/languages/en.json';
import vnMessages from './assets/json/languages/vn.json';

// Khởi tạo đối tượng chứa các thông điệp cho từng ngôn ngữ
const messages: { [key: string]: typeof enMessages } = {
    en: enMessages,
    vn: vnMessages,
};

let currentLanguage: string = 'vn'; // Ngôn ngữ mặc định là tiếng Việt

export const setLanguage = (language: string) => {
    currentLanguage = language;
};

export const getMessage = (key: keyof typeof enMessages) => {
    return messages[currentLanguage][key];
};
