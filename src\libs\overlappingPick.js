import { math } from "@xeokit/xeokit-sdk";

export class OverlappingPicker {
    constructor() {
        this.rayOriginPrecision = 0.001;
        this.rayDirectionPrecision = 0.2 * math.DEGTORAD;
        this.alreadyPicked = [];
        this.recentPickEntity = null;
        this.referenceRay = null;
        this.sentinel = {};
    }

    reset() {
        this.alreadyPicked.forEach(i => i.pickable = true);
        this.alreadyPicked = [];
        this.recentPickEntity = null;
        this.referenceRay = null;
    }

    pick(scene, pickRay, { wrapAround = false, pickCloser = false } = {}) {
        const rayChanged =
            this.referenceRay &&
            (math.distVec3(pickRay.origin, this.referenceRay.origin) > this.rayOriginPrecision ||
             math.angleVec3(pickRay.direction, this.referenceRay.direction) > this.rayDirectionPrecision);

        if (!this.referenceRay || rayChanged) {
            this.referenceRay = { origin: pickRay.origin, direction: pickRay.direction };
        }

        if (rayChanged) {
            this.alreadyPicked.forEach(i => i.pickable = true);
            this.alreadyPicked = [];
        }

        if (pickCloser) {
            for (let i = 0; i < 2; ++i) {
                if (this.alreadyPicked.length > 0)
                    this.alreadyPicked.pop().pickable = true;
            }
        }

        const innerPick = (resetAndRepeat) => {
            const pickResult = scene.pick({ origin: this.referenceRay.origin, direction: this.referenceRay.direction });
            const pickEntity = pickResult && pickResult.entity;

            if (pickEntity) {
                if (rayChanged && pickEntity === this.recentPickEntity && !pickCloser) {
                    this.alreadyPicked.push(pickEntity);
                    pickEntity.pickable = false;
                    return innerPick(true);
                } else {
                    this.alreadyPicked.push(pickEntity);
                    pickEntity.pickable = false;
                    this.recentPickEntity = pickEntity;
                    return pickResult;
                }
            } else if (wrapAround && resetAndRepeat && this.alreadyPicked.length > 0) {
                this.alreadyPicked.forEach(i => i.pickable = true);
                this.alreadyPicked = [];
                return innerPick(false);
            } else {
                if (this.alreadyPicked.length > 0 && this.alreadyPicked[this.alreadyPicked.length - 1] !== this.sentinel) {
                    this.alreadyPicked.push(this.sentinel);
                }
                this.recentPickEntity = null;
                return null;
            }
        };

        return innerPick(true);
    }
}
