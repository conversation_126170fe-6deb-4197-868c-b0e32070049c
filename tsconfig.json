{
  "compilerOptions": {
    "target": "esnext",
    "lib": [
      "DOM",
      "es2020",
      "webworker"
    ],
    "module": "esnext",
    "moduleResolution": "node",
    "baseUrl": "./",
    "paths": {
      "types": [
        "./node_modules/types/index.d.ts"
      ],
      "@types/*": ["dist/types/*"], // Ánh xạ các type
      "src/*": [
        "src/*"
      ]
    },
    "types": [
      "node"
    ],
    "declaration": true,  // Tạo tệp declaration
    "declarationDir": "./dist/types", // Lưu các tệp declaration
    "outDir": "./dist",  // Thư mục xuất ra
    "sourceMap": true,  // Bật tạo source map
    "downlevelIteration": true,  // Hỗ trợ các tính năng iteration
    "esModuleInterop": true,  // Hỗ trợ ES Module Interop
    "resolveJsonModule": true,  // Cho phép import các tệp JSON
    "forceConsistentCasingInFileNames": true, // Cưỡng chế thống nhất cách đặt tên tệp
    "strict": true,  // Bật các quy tắc nghiêm ngặt
    "noImplicitAny": true,  // Không cho phép `any` ngầm
    "skipLibCheck": true,  // Bỏ qua kiểm tra thư viện,
    "allowSyntheticDefaultImports": true,    
    "allowJs": true,  // Cho phép sử dụng JavaScript
  },
  "include": [
    "next-env.d.ts",  // Bao gồm cấu hình Next.js
    "src/**/*",   // Bao gồm tất cả các tệp trong src
  ],
  "exclude": [
    "node_modules",  // Loại trừ thư mục node_modules
    "dist"  // Loại trừ thư mục dist
  ]
}
