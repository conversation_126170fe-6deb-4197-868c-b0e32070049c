import { createUUID } from "../../../utils/utils";
import GlobalStore from "../../../core/globalStore/globalStore";
import { ContextMenu as ContextMenuXeokit } from "@xeokit/xeokit-sdk";


export class ContextMenu {
    private _generalId = createUUID();
    private _store = GlobalStore.getInstance().getAll();
    private _canvas: HTMLCanvasElement | null = null;
    private _containerView: HTMLElement | null = null;
    contextMenu: HTMLElement | null = null;
    private _isRightClick = false;
    private _startX = 0;
    private _startY = 0;
    private _onMouseDown: ((event: MouseEvent) => void) | null = null;
    private _onMouseUp: ((event: MouseEvent) => void) | null = null;
    private canvasContextMenu = new ContextMenuXeokit({
        enabled: true,
        context: {
            viewer: this._store.viewer,
        },
        items: [
            [
                {
                    title: "Isolate",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },

                    doAction: function (context: any) {
                        const scene = context.viewer.scene;
                        scene.setObjectsVisible(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.selectedObjectIds, false);
                        scene.setObjectsHighlighted(scene.selectedObjectIds, false);
                        scene.setObjectsSelected(scene.selectedObjectIds, false);
                        //Reset selections
                    }
                },
            ],
            [

                {
                    title: "Hide",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;
                        return scene.numSelectedObjects > 0;
                    },
                    doAction: function (context: any) {
                        const viewer = context.viewer;
                        const scene = viewer.scene;

                        scene.setObjectsVisible(scene.selectedObjectIds, false);
                        // scene.setObjectsXRayed(scene.xrayedObjectIds, false);
                        // scene.setObjectsXRayed(scene.selectedObjectIds, true);
                        scene.setObjectsSelected(scene.selectedObjectIds, false);
                        scene.setObjectsHighlighted(scene.selectedObjectIds, false);


                    }
                },

                {
                    title: "Show All",
                    getEnabled: function (context: any) {
                        const scene = context.viewer.scene;

                        const checkVisible = scene.numVisibleObjects < scene.numObjects
                        const checkXRayed = scene.numXRayedObjects > 0;
                        if (checkVisible || checkXRayed) {
                            return true;
                        }
                        return false;
                    },
                    doAction: function (context: any) {
                        const scene = context.viewer.scene;
                        scene.setObjectsVisible(scene.objectIds, true);
                        scene.setObjectsXRayed(scene.xrayedObjectIds, false);
                        scene.setObjectsSelected(scene.selectedObjectIds, false);
                    }
                },
                {
                    title: "test",

                    doAction: function (context: any) {
                        const scene = context.viewer.scene;
                        console.log("scene", scene);

                    }
                }
            ],
            [
                {
                    title: "View Fit All",
                    doAction: function (context: any) {
                        context.viewer.cameraFlight.flyTo({
                            aabb: context.viewer.scene.getAABB()
                        });
                    }
                }
            ]
        ]
    });

    constructor() {
        this._canvas = this._store.canvas || null;
        this._containerView = this._store.containerViewer as HTMLElement || null;
        if (!this._canvas || !this._containerView) {
            throw new Error("Canvas not found in GlobalStore.");
        }
        this.initialize();
        this.saveOriginalMaterials();
        this.changeMaterial();
        GlobalStore.getInstance().set("contextMenu", this);
    }

    //#region initialize
    public initialize() {
        this._onMouseDown = (event: MouseEvent) => {
            if (event.button === 2) {
                event.preventDefault();
                this._isRightClick = true;
                this._startX = event.clientX;
                this._startY = event.clientY;
            }
        };

        this._onMouseUp = (event: MouseEvent) => {
            if (this._isRightClick && event.button === 2) {
                const deltaX = Math.abs(event.clientX - this._startX);
                const deltaY = Math.abs(event.clientY - this._startY);

                if (deltaX < 5 && deltaY < 5) {
                    this.handleContextMenu(event);
                }
                this._isRightClick = false;
            }
        };

        this._canvas?.addEventListener('mousedown', this._onMouseDown);
        this._canvas?.addEventListener('mouseup', this._onMouseUp);
    }
    //#region handleContextMenu
    public handleContextMenu = (event: MouseEvent) => {
        // const canvasPos = this.getCanvasPosFromEvent(event);

        this.canvasContextMenu.context = { // Must set context before showing menu
            viewer: this._store.viewer
        };
        this.canvasContextMenu.show(event.pageX, event.pageY);

        event.preventDefault();
    }



    private _originalMaterials: any = null;
    //#region saveOriginalMaterials
    public saveOriginalMaterials = () => {
        const viewer = this._store.viewer!;
        this._originalMaterials = {
            xrayMaterial: {
                fill: viewer.scene.xrayMaterial.fill,
                fillAlpha: viewer.scene.xrayMaterial.fillAlpha,
                fillColor: [...viewer.scene.xrayMaterial.fillColor],
                edgeAlpha: viewer.scene.xrayMaterial.edgeAlpha,
                edgeColor: [...viewer.scene.xrayMaterial.edgeColor],
            },
            // highlightMaterial: {
            //     fill: viewer.scene.highlightMaterial.fill,
            //     edges: viewer.scene.highlightMaterial.edges,
            //     fillAlpha: viewer.scene.highlightMaterial.fillAlpha,
            //     edgeAlpha: viewer.scene.highlightMaterial.edgeAlpha,
            //     edgeColor: [...viewer.scene.highlightMaterial.edgeColor],
            // },
            // selectedMaterial: {
            //     fill: viewer.scene.selectedMaterial.fill,
            //     edges: viewer.scene.selectedMaterial.edges,
            //     fillAlpha: viewer.scene.selectedMaterial.fillAlpha,
            //     edgeAlpha: viewer.scene.selectedMaterial.edgeAlpha,
            //     edgeColor: [...viewer.scene.selectedMaterial.edgeColor],
            // }
        };
    }
    //#region resetMaterials
    public resetMaterials = () => {
        if (!this._originalMaterials) return;
        const viewer = this._store.viewer!;
        const orig = this._originalMaterials;

        Object.assign(viewer.scene.xrayMaterial, orig.xrayMaterial);
        // Object.assign(viewer.scene.highlightMaterial, orig.highlightMaterial);
        // Object.assign(viewer.scene.selectedMaterial, orig.selectedMaterial);
    }
    //#region changeMaterial
    public changeMaterial = () => {
        const viewer = this._store.viewer!;
        viewer.scene.xrayMaterial.fill = true;
        viewer.scene.xrayMaterial.fillAlpha = 0.01;
        viewer.scene.xrayMaterial.fillColor = [0, 0, 0];
        viewer.scene.xrayMaterial.edgeAlpha = 0.02;
        viewer.scene.xrayMaterial.edgeColor = [0, 0, 0];

        // viewer.scene.highlightMaterial.fill = true;
        // viewer.scene.highlightMaterial.edges = true;
        // viewer.scene.highlightMaterial.fillAlpha = 0.1;
        // viewer.scene.highlightMaterial.edgeAlpha = 0.1;
        // viewer.scene.highlightMaterial.edgeColor = [1, 1, 0];

        // viewer.scene.selectedMaterial.fill = true;
        // viewer.scene.selectedMaterial.edges = true;
        // viewer.scene.selectedMaterial.fillAlpha = 0.1;
        // viewer.scene.selectedMaterial.edgeAlpha = 0.15;
        // viewer.scene.selectedMaterial.edgeColor = [0, 1, 1];
    }
    //#region removeEventListeners
    public removeEventListeners() {
        if (this._onMouseDown) {
            this._canvas?.removeEventListener('mousedown', this._onMouseDown);
            this._onMouseDown = null;
        }
        if (this._onMouseUp) {
            this._canvas?.removeEventListener('mouseup', this._onMouseUp);
            this._onMouseUp = null;
        }
    }

}