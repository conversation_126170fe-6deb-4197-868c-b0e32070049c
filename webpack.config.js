import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);


export default {
  entry: './src/index.ts',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'index.js',
    library: {
      type: 'module'
    }
  },
  experiments: {
    outputModule: true, // Enable ESM
    asyncWebAssembly: true, // Enable WASM support
  },
  resolve: {
    mainFiles: ["index"],
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'ifcdb.wasm': path.resolve(__dirname, 'node_modules/@xeokit/xeokit-sdk/dist/web-ifc.wasm')
    },
    extensions: ['.ts', '.js', '.css', '.glb', '.wasm'],
    fallback: {
      "path": "path-browserify",
      "fs": false, // fs is not available in browser, disable it
      "crypto": "crypto-browserify",
      "stream": "stream-browserify",
      "buffer": "buffer",
      "util": "util",
      "assert": "assert",
      "url": "url",
      "querystring": "querystring-es3",
      "os": "os-browserify/browser",
      "https": "https-browserify",
      "http": "stream-http",
      "zlib": "browserify-zlib",
      "vm": "vm-browserify"
    }
  },
  module: {
    rules: [
      {
        test: /\.css$/i, // Add rule for processing CSS
        use: ['style-loader', 'css-loader'], // style-loader inserts CSS into DOM, css-loader processes CSS files
      },
      {
        test: /\.(png|gif|jpg|jpeg|svg|xml)$/,
        type: "asset/inline",
      },
      {
        test: /\.ts$/,
        use: 'ts-loader',
        exclude: /node_modules/,
      },
      {
        test: /\.worker\.js$/,
        use: { loader: "worker-loader" },
      },
      {
        test: /\.json$/i,
        type: 'json', // Properly handle JSON files without converting to base64
      },
      {
        test: /\.glb$/i, // Define rule for `.glb` files
        use: [
          {
            loader: 'file-loader', // Use file-loader for .glb files
            options: {
              name: 'assets/glb/[name].[ext]',
            },
          },
        ],
      },
      {
        test: /\.wasm$/,
        type: 'asset/resource',
        generator: {
          filename: 'wasm/[name][ext]'
        }
      },
    ]
  },

  mode: 'production',
  devtool: 'source-map'
};